<?php

namespace Tests\Feature;

use App\Models\Brand;
use App\Models\MobileModel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ModelViewRedirectTest extends TestCase
{
    use RefreshDatabase;

    private MobileModel $model;
    private Brand $brand;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test brand and model
        $this->brand = Brand::factory()->create([
            'name' => 'Test Brand',
            'slug' => 'test-brand',
            'is_active' => true,
        ]);

        $this->model = MobileModel::factory()->create([
            'brand_id' => $this->brand->id,
            'name' => 'Test Model',
            'slug' => 'test-brand-test-model',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function guest_user_can_access_public_model_view()
    {
        $response = $this->get("/models/{$this->model->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/model-view')
            ->has('model')
            ->where('model.name', 'Test Model')
            ->has('isSubscribed')
            ->has('hasUnlimitedAccess')
            ->has('requiresSignup')
        );
    }

    /** @test */
    public function guest_user_can_access_public_model_view_with_id()
    {
        $response = $this->get("/models/{$this->model->id}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/model-view')
            ->has('model')
            ->where('model.name', 'Test Model')
        );
    }

    /** @test */
    public function regular_user_can_access_public_model_view()
    {
        $user = User::factory()->create([
            'is_admin' => false,
            'role' => 'user',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($user)->get("/models/{$this->model->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/model-view')
            ->has('model')
            ->where('model.name', 'Test Model')
        );
    }

    /** @test */
    public function admin_user_can_access_admin_model_view()
    {
        $admin = User::factory()->create([
            'is_admin' => true,
            'role' => 'admin',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($admin)->get("/admin/models/{$this->model->slug}/view");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/model-details')
            ->has('model')
            ->where('model.name', 'Test Model')
        );
    }

    /** @test */
    public function content_manager_can_access_admin_model_view()
    {
        $contentManager = User::factory()->create([
            'is_admin' => false,
            'role' => 'content_manager',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($contentManager)->get("/admin/models/{$this->model->slug}/view");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('search/model-details')
            ->has('model')
            ->where('model.name', 'Test Model')
        );
    }

    /** @test */
    public function public_model_view_uses_public_layout()
    {
        $response = $this->get("/models/{$this->model->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/model-view')
            ->has('model')
            ->where('model.name', 'Test Model')
            ->has('isSubscribed')
            ->has('hasUnlimitedAccess')
            ->has('requiresSignup')
        );
    }

    /** @test */
    public function admin_activity_is_logged_for_admin_model_view()
    {
        $admin = User::factory()->create([
            'is_admin' => true,
            'role' => 'admin',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $this->actingAs($admin)->get("/admin/models/{$this->model->slug}/view");

        // Check that some activity was logged for the admin user
        $this->assertDatabaseHas('user_activity_logs', [
            'user_id' => $admin->id,
        ]);
    }

    /** @test */
    public function model_get_public_url_returns_public_route()
    {
        $expectedUrl = route('models.show', $this->model->slug);
        $actualUrl = $this->model->getPublicUrl();

        $this->assertEquals($expectedUrl, $actualUrl);
    }

    /** @test */
    public function model_get_public_url_uses_slug_when_available()
    {
        // Test that getPublicUrl uses slug when available
        $expectedUrl = route('public.models.show', $this->model->slug);
        $actualUrl = $this->model->getPublicUrl();

        $this->assertEquals($expectedUrl, $actualUrl);
    }

    /** @test */
    public function approved_user_can_access_public_model_view()
    {
        $approvedUser = User::factory()->create([
            'is_admin' => false,
            'role' => 'user',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($approvedUser)->get("/models/{$this->model->slug}");

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page
            ->component('public/model-view')
            ->has('model')
            ->where('model.name', 'Test Model')
        );
    }

    /** @test */
    public function non_admin_user_cannot_access_admin_model_view()
    {
        // Test user who has admin flag but not admin role
        $user = User::factory()->create([
            'is_admin' => false,
            'role' => 'user',
            'status' => 'active',
            'approval_status' => 'approved',
        ]);

        $response = $this->actingAs($user)->get("/admin/models/{$this->model->slug}/view");

        $response->assertStatus(403);
    }
}
